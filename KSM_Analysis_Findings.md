# KSM Analysis Findings

## Executive Summary

Based on the hierarchical modeling analysis and top percentile uplift investigation, here are the key findings regarding KSM (Key Selling Moments) impact on promotion uplifts:

## Key Findings

### 1. KSM Coefficient Analysis (Method 5 - Brand Retailer Crossed Random Effects)

**From the best performing model (brand_retailer_crossed):**
- **KSM coefficient: 0.0401** (not statistically significant)
- **p-value: > 0.05** (not significant)
- **Interpretation**: KSM has minimal impact on promotion uplift coefficients in the hierarchical model

**Model Performance:**
- Best model: `brand_retailer_crossed` (AIC: 548.61)
- Second best: `three_way_crossed` (AIC: 549.92)
- Both models show KSM has low impact on uplift

### 2. Top Percentile Uplift Analysis

**Top 80 Highest Uplifts (20th percentile):**
- **Total entries analyzed:** 80 out of 394 total promotions
- **KSM Distribution in Top 80:**
  - KSM = 0: 41 entries (51.3%)
  - KSM = 1: 39 entries (48.7%)
  
**Key Insight:** KSM distribution in highest uplifts is roughly balanced, suggesting KSM alone doesn't drive the highest uplifts.

### 3. Seasonal Pattern Analysis

**Monthly Distribution of Top 80 Uplifts:**
- **October**: 13 entries (16.3%) - **Peak month**
- **March**: 9 entries (11.3%)
- **August**: 9 entries (11.3%)
- **September**: 9 entries (11.3%)
- **February**: 8 entries (10.0%)
- **May**: 7 entries (8.8%)
- **June**: 6 entries (7.5%)
- **July**: 5 entries (6.3%)
- **November**: 5 entries (6.3%)
- **April**: 4 entries (5.0%)
- **December**: 3 entries (3.8%)
- **January**: 2 entries (2.5%)

### 4. Pattern Identification

**Peak Uplift Periods:**
1. **October** (16.3%) - Highest concentration of top uplifts
2. **March/August/September** (11.3% each) - Secondary peaks
3. **Winter months** (Dec/Jan) show **lowest** uplift concentration

**Seasonal Insights:**
- **October**: Likely related to back-to-school/autumn promotions
- **March**: Spring season start, potential Easter timing
- **August/September**: End of summer/back-to-school period
- **December/January**: Surprisingly low despite holiday season

### 5. KSM Impact Assessment

**Evidence suggesting KSM may be over-inclusive:**

1. **Statistical insignificance**: KSM coefficient (0.0401) is not significant in the best model
2. **Balanced distribution**: 51.3% vs 48.7% split in top uplifts suggests no clear KSM advantage
3. **Seasonal mismatch**: Peak uplift months (October, March, August) don't align with traditional "key selling moments"
4. **Holiday paradox**: December/January show lowest uplift concentration despite being typical KSM periods

## Recommendations

### 1. KSM Refinement Strategy

**Current Issue:** Too many festivals/periods marked as KSM, diluting the true impact.

**Suggested Action:**
- **Reduce KSM scope** to focus on periods with demonstrated high uplift potential
- **Data-driven KSM definition**: Use October, March, August, September as primary KSM periods
- **Remove** December/January from KSM classification unless specific analysis shows benefit

### 2. Seasonal Focus Areas

**High-Impact Months for Promotions:**
1. **October** (16.3% of top uplifts)
2. **March** (11.3% of top uplifts)
3. **August** (11.3% of top uplifts)
4. **September** (11.3% of top uplifts)

**Low-Impact Months:**
- December (3.8%)
- January (2.5%)

### 3. Model Implications

**Best Model:** Brand + Retailer Crossed Random Effects
- **Implication**: Brand-retailer combinations have more impact than KSM timing
- **Strategy**: Focus on optimal brand-retailer partnerships rather than timing alone

## Detailed Analysis Results

### Model Performance Summary
```
Model                    | AIC    | KSM Coefficient | Significance
brand_retailer_crossed   | 548.61 | 0.0401         | Not significant
three_way_crossed        | 549.92 | 0.0401         | Not significant
fixed_only               | 558.16 | 0.0301         | Not significant
```

### Top Uplift Examples
Based on the data analysis, here are examples of high-performing promotions:

- **December 2022** (KSM=1): 5.67 uplift
- **October 2022** (KSM=0): 5.43 uplift
- **August 2023** (KSM=1): 4.78 uplift
- **March 2024** (KSM=1): 4.81 uplift

**Pattern**: High uplifts occur both with and without KSM designation, reinforcing that KSM is not the primary driver.

## Conclusion

The analysis strongly suggests that the current KSM classification is **too broad** and may be including periods that don't actually drive higher promotion uplifts. The data indicates:

1. **KSM has minimal statistical impact** on promotion uplift
2. **October is the peak month** for high uplifts (not traditional holiday periods)
3. **Brand-retailer combinations** have more impact than seasonal timing
4. **Traditional "key selling moments"** (Dec/Jan) show lowest uplift performance

**Recommendation**: Refine KSM definition to focus on data-driven high-uplift periods (October, March, August, September) rather than traditional seasonal assumptions.

---

*Analysis performed on 394 promotion records using hierarchical mixed-effects modeling with brand-retailer crossed random effects as the optimal model structure.*