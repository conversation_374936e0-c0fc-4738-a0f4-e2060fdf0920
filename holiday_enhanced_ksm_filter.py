#!/usr/bin/env python3
"""
Holiday-Enhanced KSM Filter
Creates an enhanced KSM filter that combines the Top70pct approach with specific holiday targeting
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load promotion and holiday data"""
    # Load promotion data
    df_promo = pd.read_csv('demelted_data.csv')
    df_promo['ABI Start'] = pd.to_datetime(df_promo['ABI Start'])
    df_promo['ABI End'] = pd.to_datetime(df_promo['ABI End'])
    df_promo['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df_promo['ABI MS Promo Uplift - rel'], errors='coerce')
    df_promo = df_promo.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])
    
    # Load holiday data
    df_holidays = pd.read_csv('Holidays.csv')
    df_holidays['date'] = pd.to_datetime(df_holidays['date']).dt.tz_localize(None)
    
    return df_promo, df_holidays

def create_holiday_enhanced_ksm_filter(df_promo, df_holidays):
    """
    Create enhanced KSM filter combining Top70pct with specific holiday targeting
    """
    print("Creating Holiday-Enhanced KSM Filter...")
    
    # Define top-performing holidays and their optimal timing
    top_holidays = {
        'Toussaint': {'days_before': 5, 'priority': 1},
        'Assomption': {'days_before': 2, 'priority': 1}, 
        '11 novembre': {'days_before': 5, 'priority': 1},
        'Lundi de Pâques': {'days_before': 6, 'priority': 2},
        '1er mai': {'days_before': 5, 'priority': 2},
        '1er janvier': {'days_before': 5, 'priority': 2}
    }
    
    # Calculate 70th percentile threshold
    threshold_70 = np.percentile(df_promo['ABI_MS_Uplift_Rel_Raw'], 70)
    
    # Initialize enhanced KSM columns
    df_promo['KSM_Holiday_Enhanced'] = 0
    df_promo['KSM_Holiday_Match'] = ''
    df_promo['KSM_Holiday_Priority'] = 0
    
    # Track matches for reporting
    holiday_matches = []
    
    for idx, promo in df_promo.iterrows():
        promo_start = promo['ABI Start']
        original_ksm = promo['KSM']
        uplift = promo['ABI_MS_Uplift_Rel_Raw']
        
        # Check for holiday matches
        holiday_match = False
        matched_holiday = ''
        holiday_priority = 0
        
        for hidx, holiday in df_holidays.iterrows():
            holiday_date = holiday['date']
            holiday_name = holiday['bank_holiday_fr']
            
            if holiday_name in top_holidays:
                optimal_days = top_holidays[holiday_name]['days_before']
                priority = top_holidays[holiday_name]['priority']
                
                # Check if promotion starts within optimal window
                days_diff = (holiday_date - promo_start).days
                
                # Allow some flexibility around optimal timing (±2 days)
                if optimal_days - 2 <= days_diff <= optimal_days + 2:
                    holiday_match = True
                    matched_holiday = holiday_name
                    holiday_priority = priority
                    
                    holiday_matches.append({
                        'promo_start': promo_start,
                        'holiday_date': holiday_date,
                        'holiday_name': holiday_name,
                        'days_before': days_diff,
                        'optimal_days': optimal_days,
                        'uplift': uplift,
                        'original_ksm': original_ksm
                    })
                    break
        
        # Enhanced KSM logic:
        # 1. If it's a top holiday match, always mark as KSM=1
        # 2. If original KSM=1 and uplift >= 70th percentile, mark as KSM=1
        # 3. Otherwise, KSM=0
        
        if holiday_match and holiday_priority == 1:  # Tier 1 holidays
            df_promo.loc[idx, 'KSM_Holiday_Enhanced'] = 1
            df_promo.loc[idx, 'KSM_Holiday_Match'] = matched_holiday
            df_promo.loc[idx, 'KSM_Holiday_Priority'] = holiday_priority
        elif holiday_match and holiday_priority == 2:  # Tier 2 holidays
            df_promo.loc[idx, 'KSM_Holiday_Enhanced'] = 1
            df_promo.loc[idx, 'KSM_Holiday_Match'] = matched_holiday
            df_promo.loc[idx, 'KSM_Holiday_Priority'] = holiday_priority
        elif original_ksm == 1 and uplift >= threshold_70:  # Top70pct filter
            df_promo.loc[idx, 'KSM_Holiday_Enhanced'] = 1
        else:
            df_promo.loc[idx, 'KSM_Holiday_Enhanced'] = 0
    
    # Convert to categorical
    df_promo['KSM_Holiday_Enhanced'] = df_promo['KSM_Holiday_Enhanced'].astype('category')
    
    # Report results
    print(f"\nHoliday-Enhanced KSM Filter Results:")
    print(f"70th percentile threshold: {threshold_70:.3f}")
    print(f"Original KSM=1 entries: {df_promo['KSM'].sum()}")
    print(f"Top70pct KSM entries: {((df_promo['KSM'] == 1) & (df_promo['ABI_MS_Uplift_Rel_Raw'] >= threshold_70)).sum()}")
    print(f"Holiday-Enhanced KSM=1 entries: {df_promo['KSM_Holiday_Enhanced'].astype(int).sum()}")
    print(f"Holiday matches found: {len(holiday_matches)}")
    
    # Holiday breakdown
    holiday_breakdown = df_promo[df_promo['KSM_Holiday_Match'] != '']['KSM_Holiday_Match'].value_counts()
    if len(holiday_breakdown) > 0:
        print(f"\nHoliday matches breakdown:")
        for holiday, count in holiday_breakdown.items():
            print(f"  {holiday}: {count} promotions")
    
    # Priority breakdown
    priority_breakdown = df_promo[df_promo['KSM_Holiday_Priority'] > 0]['KSM_Holiday_Priority'].value_counts()
    if len(priority_breakdown) > 0:
        print(f"\nPriority breakdown:")
        for priority, count in priority_breakdown.items():
            print(f"  Tier {priority}: {count} promotions")
    
    return df_promo, holiday_matches

def compare_ksm_filters(df_promo):
    """Compare different KSM filter approaches"""
    print(f"\n" + "="*60)
    print("KSM FILTER COMPARISON")
    print("="*60)
    
    # Calculate different filters
    threshold_70 = np.percentile(df_promo['ABI_MS_Uplift_Rel_Raw'], 70)
    
    original_ksm = df_promo['KSM'].astype(int).sum()
    top70_ksm = ((df_promo['KSM'] == 1) & (df_promo['ABI_MS_Uplift_Rel_Raw'] >= threshold_70)).sum()
    holiday_enhanced_ksm = df_promo['KSM_Holiday_Enhanced'].astype(int).sum()
    
    print(f"Filter Comparison:")
    print(f"{'Filter Type':<25} {'KSM=1 Count':<12} {'Reduction':<12} {'Strategy'}")
    print("-" * 70)
    print(f"{'Original KSM':<25} {original_ksm:<12} {'-':<12} {'All flagged periods'}")
    print(f"{'Top70pct Filter':<25} {top70_ksm:<12} {f'-{original_ksm-top70_ksm}':<12} {'High uplift only'}")
    print(f"{'Holiday-Enhanced':<25} {holiday_enhanced_ksm:<12} {f'+{holiday_enhanced_ksm-top70_ksm}':<12} {'High uplift + holidays'}")
    
    # Performance comparison
    print(f"\nPerformance by filter:")
    filters = {
        'Original KSM': df_promo['KSM'] == 1,
        'Top70pct': (df_promo['KSM'] == 1) & (df_promo['ABI_MS_Uplift_Rel_Raw'] >= threshold_70),
        'Holiday-Enhanced': df_promo['KSM_Holiday_Enhanced'] == 1
    }
    
    for filter_name, filter_mask in filters.items():
        if filter_mask.sum() > 0:
            mean_uplift = df_promo[filter_mask]['ABI_MS_Uplift_Rel_Raw'].mean()
            print(f"  {filter_name:<20}: {mean_uplift:.3f} mean uplift")

def generate_enhanced_ksm_code(df_promo):
    """Generate code for the enhanced KSM filter to use in notebook"""
    print(f"\n" + "="*60)
    print("ENHANCED KSM FILTER CODE FOR NOTEBOOK")
    print("="*60)
    
    code = '''
# Holiday-Enhanced KSM Filter
# Combines Top70pct approach with specific French holiday targeting

def create_holiday_enhanced_ksm(df):
    """Create holiday-enhanced KSM filter"""
    
    # Load holiday data
    try:
        df_holidays = pd.read_csv('Holidays.csv')
        df_holidays['date'] = pd.to_datetime(df_holidays['date']).dt.tz_localize(None)
    except:
        print("Warning: Holidays.csv not found, using Top70pct filter only")
        threshold_70 = np.percentile(df['ABI_MS_Uplift_Rel_Raw'], 70)
        df['KSM_Holiday_Enhanced'] = np.where(
            (df['KSM'].astype(int) == 1) & (df['ABI_MS_Uplift_Rel_Raw'] >= threshold_70), 1, 0
        )
        return df
    
    # Top-performing holidays with optimal timing
    top_holidays = {
        'Toussaint': 5,        # 5 days before (highest performer: 7.904 uplift)
        'Assomption': 2,       # 2 days before (5.880 uplift)
        '11 novembre': 5,      # 5 days before (4.044 uplift)
        'Lundi de Pâques': 6,  # 6 days before (4.943 uplift)
        '1er mai': 5,          # 5 days before (5.146 uplift)
        '1er janvier': 5       # 5 days before (5.020 uplift)
    }
    
    # Calculate 70th percentile threshold
    threshold_70 = np.percentile(df['ABI_MS_Uplift_Rel_Raw'], 70)
    
    # Initialize enhanced KSM
    df['KSM_Holiday_Enhanced'] = 0
    
    for idx, row in df.iterrows():
        promo_start = row['ABI Start']
        original_ksm = row['KSM']
        uplift = row['ABI_MS_Uplift_Rel_Raw']
        
        # Check for holiday matches
        holiday_match = False
        for _, holiday in df_holidays.iterrows():
            holiday_date = holiday['date']
            holiday_name = holiday['bank_holiday_fr']
            
            if holiday_name in top_holidays:
                optimal_days = top_holidays[holiday_name]
                days_diff = (holiday_date - promo_start).days
                
                # Match if within ±2 days of optimal timing
                if optimal_days - 2 <= days_diff <= optimal_days + 2:
                    holiday_match = True
                    break
        
        # Enhanced KSM logic
        if holiday_match or (original_ksm == 1 and uplift >= threshold_70):
            df.loc[idx, 'KSM_Holiday_Enhanced'] = 1
    
    df['KSM_Holiday_Enhanced'] = df['KSM_Holiday_Enhanced'].astype('category')
    
    print(f"Holiday-Enhanced KSM created:")
    print(f"  Original KSM=1: {df['KSM'].astype(int).sum()}")
    print(f"  Enhanced KSM=1: {df['KSM_Holiday_Enhanced'].astype(int).sum()}")
    
    return df

# Add this function to your feature engineering and call it after creating KSM_Top70pct
# Then use 'KSM_Holiday_Enhanced' in your model formulas instead of 'KSM_Top70pct'
'''
    
    print(code)
    
    # Save to file
    with open('holiday_enhanced_ksm_code.py', 'w') as f:
        f.write(code)
    
    print(f"\n✅ Code saved to 'holiday_enhanced_ksm_code.py'")
    print(f"📋 To implement: Copy this function into your notebook's feature engineering section")

def main():
    """Main analysis function"""
    print("HOLIDAY-ENHANCED KSM FILTER CREATION")
    print("="*60)
    
    # Load data
    df_promo, df_holidays = load_data()
    print(f"Loaded {len(df_promo)} promotions and {len(df_holidays)} holidays")
    
    # Create enhanced filter
    df_enhanced, holiday_matches = create_holiday_enhanced_ksm_filter(df_promo, df_holidays)
    
    # Compare filters
    compare_ksm_filters(df_enhanced)
    
    # Generate code for notebook
    generate_enhanced_ksm_code(df_enhanced)
    
    print(f"\n" + "="*60)
    print("HOLIDAY-ENHANCED KSM FILTER COMPLETE!")
    print("="*60)
    print("This filter combines:")
    print("1. Top 70 percentile uplift filtering")
    print("2. Specific French holiday targeting")
    print("3. Optimal promotional timing windows")
    print("\nResult: More precise KSM definition with proven holiday performance")
    
    return df_enhanced, holiday_matches

if __name__ == "__main__":
    df_enhanced, holiday_matches = main()
