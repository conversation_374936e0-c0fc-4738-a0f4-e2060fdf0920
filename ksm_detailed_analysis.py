#!/usr/bin/env python3
"""
Detailed KSM Analysis for High Uplift Periods
Focuses on identifying actual key selling moments that drive high uplifts
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Load and prepare the data with enhanced date features"""
    print("Loading data...")
    df = pd.read_csv('demelted_data.csv')
    
    # Convert dates
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    
    # Extract comprehensive date features
    df['Start_Month'] = df['ABI Start'].dt.month
    df['Start_Year'] = df['ABI Start'].dt.year
    df['Start_Month_Name'] = df['ABI Start'].dt.strftime('%B')
    df['Start_Week'] = df['ABI Start'].dt.isocalendar().week
    df['Start_DayOfWeek'] = df['ABI Start'].dt.dayofweek
    df['Start_Quarter'] = df['ABI Start'].dt.quarter
    
    # Create season mapping
    seasons = {
        12: 'Winter', 1: 'Winter', 2: 'Winter',
        3: 'Spring', 4: 'Spring', 5: 'Spring',
        6: 'Summer', 7: 'Summer', 8: 'Summer',
        9: 'Fall', 10: 'Fall', 11: 'Fall'
    }
    df['Season'] = df['Start_Month'].map(seasons)
    
    # Create festival periods based on months
    festival_periods = {
        12: 'Christmas/New Year',
        1: 'Christmas/New Year', 
        2: 'Valentine/Carnival',
        3: 'Easter/Spring',
        4: 'Easter/Spring',
        5: 'May Day/Spring',
        6: 'Summer Start',
        7: 'Summer Peak',
        8: 'Summer End',
        9: 'Back to School',
        10: 'Oktoberfest/Fall',
        11: 'Thanksgiving/Fall'
    }
    df['Festival_Period'] = df['Start_Month'].map(festival_periods)
    
    print(f"Data loaded: {len(df)} rows")
    return df

def analyze_uplift_percentiles(df):
    """Analyze uplift distribution and create percentile filters"""
    print("\n" + "="*80)
    print("UPLIFT PERCENTILE ANALYSIS")
    print("="*80)
    
    uplift_col = 'ABI MS Promo Uplift - rel'
    
    # Calculate percentiles
    percentiles = [50, 70, 80, 85, 90, 95, 97, 99]
    percentile_values = {}
    
    print("Uplift Distribution Statistics:")
    print(f"Mean: {df[uplift_col].mean():.3f}")
    print(f"Median: {df[uplift_col].median():.3f}")
    print(f"Std Dev: {df[uplift_col].std():.3f}")
    print(f"Min: {df[uplift_col].min():.3f}")
    print(f"Max: {df[uplift_col].max():.3f}")
    
    print("\nPercentile Thresholds:")
    for p in percentiles:
        value = np.percentile(df[uplift_col], p)
        percentile_values[p] = value
        count = len(df[df[uplift_col] >= value])
        print(f"{p}th percentile: {value:.3f} ({count} entries)")
    
    return percentile_values

def analyze_ksm_in_high_uplifts(df, percentile_values):
    """Analyze KSM patterns in high uplift percentiles"""
    print("\n" + "="*80)
    print("KSM ANALYSIS IN HIGH UPLIFT PERCENTILES")
    print("="*80)
    
    uplift_col = 'ABI MS Promo Uplift - rel'
    results = {}
    
    for percentile, threshold in percentile_values.items():
        if percentile >= 70:  # Focus on top 30% and above
            print(f"\n--- TOP {100-percentile}% UPLIFTS (≥{threshold:.3f}) ---")
            
            # Filter high uplift entries
            high_uplift = df[df[uplift_col] >= threshold].copy()
            
            # KSM analysis
            ksm_stats = high_uplift['KSM'].value_counts()
            ksm_pct = (ksm_stats / len(high_uplift) * 100).round(1)
            
            print(f"Total entries: {len(high_uplift)}")
            print("KSM Distribution:")
            for ksm, count in ksm_stats.items():
                print(f"  KSM {ksm}: {count} entries ({ksm_pct[ksm]}%)")
            
            # Compare with overall KSM distribution
            overall_ksm = df['KSM'].value_counts()
            overall_pct = (overall_ksm / len(df) * 100).round(1)
            
            print("Overall KSM Distribution:")
            for ksm, count in overall_ksm.items():
                print(f"  KSM {ksm}: {count} entries ({overall_pct[ksm]}%)")
            
            # Calculate uplift lift for KSM=1 vs KSM=0
            ksm_1_uplift = high_uplift[high_uplift['KSM'] == 1][uplift_col].mean()
            ksm_0_uplift = high_uplift[high_uplift['KSM'] == 0][uplift_col].mean()
            
            if pd.notna(ksm_1_uplift) and pd.notna(ksm_0_uplift):
                uplift_lift = ((ksm_1_uplift - ksm_0_uplift) / ksm_0_uplift * 100)
                print(f"Uplift lift (KSM=1 vs KSM=0): {uplift_lift:.1f}%")
            
            results[percentile] = {
                'data': high_uplift,
                'ksm_stats': ksm_stats,
                'ksm_1_uplift': ksm_1_uplift,
                'ksm_0_uplift': ksm_0_uplift
            }
    
    return results

def identify_peak_periods(df, percentile=80):
    """Identify specific periods with highest uplifts"""
    print("\n" + "="*80)
    print(f"PEAK PERIODS ANALYSIS (Top {100-percentile}%)")
    print("="*80)
    
    threshold = np.percentile(df['ABI MS Promo Uplift - rel'], percentile)
    high_uplift = df[df['ABI MS Promo Uplift - rel'] >= threshold].copy()
    
    # Monthly analysis
    monthly_stats = high_uplift.groupby('Start_Month_Name').agg({
        'ABI MS Promo Uplift - rel': ['count', 'mean', 'max'],
        'KSM': 'sum'
    }).round(3)
    
    print("Monthly Distribution of High Uplifts:")
    print(monthly_stats)
    
    # Festival period analysis
    festival_stats = high_uplift.groupby('Festival_Period').agg({
        'ABI MS Promo Uplift - rel': ['count', 'mean', 'max'],
        'KSM': ['sum', 'mean']
    }).round(3)
    
    print("\nFestival Period Distribution:")
    print(festival_stats)
    
    # Top 20 specific dates
    top_dates = high_uplift.nlargest(20, 'ABI MS Promo Uplift - rel')[
        ['ABI Start', 'ABI MS Promo Uplift - rel', 'KSM', 'Festival_Period', 'Start_Month_Name', 'ABI SKU']
    ]
    
    print("\nTop 20 Highest Uplift Dates:")
    for idx, row in top_dates.iterrows():
        print(f"  {row['ABI Start'].strftime('%Y-%m-%