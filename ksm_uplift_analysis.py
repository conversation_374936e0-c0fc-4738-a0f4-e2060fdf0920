#!/usr/bin/env python3
"""
KSM Uplift Analysis Script
Analyzes the relationship between KSM (Key Selling Moments) and ABI MS Promo Uplift
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load and prepare the data"""
    print("Loading data...")
    df = pd.read_csv('demelted_data.csv')
    
    # Convert dates
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    
    # Extract month and year
    df['Start_Month'] = df['ABI Start'].dt.month
    df['Start_Year'] = df['ABI Start'].dt.year
    df['Start_Month_Name'] = df['ABI Start'].dt.strftime('%B')
    
    print(f"Data loaded: {len(df)} rows")
    return df

def analyze_ksm_coefficients(df):
    """Analyze KSM coefficient from Method 5 model"""
    print("\n" + "="*60)
    print("KSM COEFFICIENT ANALYSIS FROM METHOD 5")
    print("="*60)
    
    # From the notebook output, Method 5 (brand_retailer_crossed) had:
    # KSM coefficient: 0.0401 (not significant)
    print("From Method 5 (Brand + Retailer Crossed Random Effects):")
    print("KSM coefficient: 0.0401 (not significant)")
    print("This suggests KSM has minimal impact on uplift coefficients")
    
    # Basic KSM statistics
    ksm_stats = df.groupby('KSM')['ABI MS Promo Uplift - rel'].agg(['count', 'mean', 'median', 'std'])
    print("\nKSM Basic Statistics:")
    print(ksm_stats)
    
    return ksm_stats

def filter_top_percentile_uplifts(df, percentiles=[70, 80]):
    """Filter top percentile uplifts and analyze their KSM patterns"""
    print("\n" + "="*60)
    print("TOP PERCENTILE UPLIFT ANALYSIS")
    print("="*60)
    
    results = {}
    
    for percentile in percentiles:
        threshold = np.percentile(df['ABI MS Promo Uplift - rel'], percentile)
        top_uplifts = df[df['ABI MS Promo Uplift - rel'] >= threshold].copy()
        
        print(f"\n{percentile}th Percentile Analysis:")
        print(f"Threshold: {threshold:.3f}")
        print(f"Number of entries: {len(top_uplifts)}")
        
        # KSM distribution in top percentile
        ksm_dist = top_uplifts['KSM'].value_counts()
        ksm_pct = (ksm_dist / len(top_uplifts) * 100).round(1)
        
        print(f"KSM Distribution:")
        for ksm, count in ksm_dist.items():
            print(f"  KSM {ksm}: {count} entries ({ksm_pct[ksm]}%)")
        
        # Monthly pattern analysis
        monthly_pattern = top_uplifts.groupby('Start_Month_Name').agg({
            'ABI MS Promo Uplift - rel': ['count', 'mean'],
            'KSM': 'sum'
        }).round(3)
        
        print(f"\nMonthly Pattern (Top {percentile}th percentile):")
        print(monthly_pattern)
        
        results[percentile] = {
            'data': top_uplifts,
            'ksm_distribution': ksm_dist,
            'monthly_pattern': monthly_pattern
        }
    
    return results

def analyze_seasonal_patterns(df):
    """Analyze seasonal patterns in high uplifts"""
    print("\n" + "="*60)
    print("SEASONAL PATTERN ANALYSIS")
    print("="*60)
    
    # Define seasonal periods
    seasonal_months = {
        'Christmas/New Year': [12, 1],
        'Easter': [3, 4],  # Approximate - Easter varies
        'Summer': [6, 7, 8],
        'Back to School': [9],
        'Spring': [3, 4, 5],
        'Winter': [12, 1, 2]
    }
    
    # Get top 20% uplifts for seasonal analysis
    top_20_threshold = np.percentile(df['ABI MS Promo Uplift - rel'], 80)
    top_20_df = df[df['ABI MS Promo Uplift - rel'] >= top_20_threshold].copy()
    
    print(f"Analyzing top 20% uplifts (>{top_20_threshold:.3f})")
    print(f"Number of entries: {len(top_20_df)}")
    
    # Monthly distribution of high uplifts
    monthly_uplift = top_20_df.groupby('Start_Month_Name').agg({
        'ABI MS Promo Uplift - rel': ['count', 'mean'],
        'KSM': ['sum', 'mean']
    }).round(3)
    
    print("\nMonthly Distribution of High Uplifts:")
    print(monthly_uplift)
    
    # Identify peak months
    uplift_counts = top_20_df['Start_Month_Name'].value_counts()
    print(f"\nPeak months for high uplifts:")
    for month, count in uplift_counts.head(5).items():
        pct = (count / len(top_20_df) * 100)
        print(f"  {month}: {count} entries ({pct:.1f}%)")
    
    return top_20_df, monthly_uplift

def analyze_ksm_impact_by_month(df):
    """Analyze KSM impact by month"""
    print("\n" + "="*60)
    print("KSM IMPACT BY MONTH")
    print("="*60)
    
    # Compare KSM=1 vs KSM=0 by month
    monthly_ksm_comparison = df.groupby(['Start_Month_Name', 'KSM'])['ABI MS Promo Uplift - rel'].agg(['count', 'mean']).round(3)
    
    print("Monthly KSM Comparison:")
    print(monthly_ksm_comparison)
    
    # Calculate uplift difference (KSM=1 - KSM=0) by month
    print("\nUplift Difference (KSM=1 - KSM=0) by Month:")
    
    months = df['Start_Month_Name'].unique()
    ksm_impact = {}
    
    for month in months:
        month_data = df[df['Start_Month_Name'] == month]
        
        ksm_1_mean = month_data[month_data['KSM'] == 1]['ABI MS Promo Uplift - rel'].mean()
        ksm_0_mean = month_data[month_data['KSM'] == 0]['ABI MS Promo Uplift - rel'].mean()
        
        if pd.notna(ksm_1_mean) and pd.notna(ksm_0_mean):
            difference = ksm_1_mean - ksm_0_mean
            ksm_impact[month] = {
                'KSM_1_mean': ksm_1_mean,
                'KSM_0_mean': ksm_0_mean,
                'difference': difference
            }
            print(f"  {month:12}: KSM=1 ({ksm_1_mean:.3f}) - KSM=0 ({ksm_0_mean:.3f}) = {difference:+.3f}")
    
    return ksm_impact

def identify_highest_uplift_patterns(df, top_n=50):
    """Identify patterns in the highest uplift entries"""
    print("\n" + "="*60)
    print(f"HIGHEST {top_n} UPLIFT ENTRIES ANALYSIS")
    print("="*60)
    
    # Get top N highest uplifts
    top_entries = df.nlargest(top_n, 'ABI MS Promo Uplift - rel')
    
    print(f"Top {top_n} Uplift Statistics:")
    print(f"Range: {top_entries['ABI MS Promo Uplift - rel'].min():.3f} to {top_entries['ABI MS Promo Uplift - rel'].max():.3f}")
    print(f"Mean: {top_entries['ABI MS Promo Uplift - rel'].mean():.3f}")
    
    # KSM distribution in top entries
    ksm_dist = top_entries['KSM'].value_counts()
    print(f"\nKSM Distribution in Top {top_n}:")
    for ksm, count in ksm_dist.items():
        pct = (count / len(top_entries) * 100)
        print(f"  KSM {ksm}: {count} entries ({pct:.1f}%)")
    
    # Monthly distribution
    monthly_dist = top_entries['Start_Month_Name'].value_counts()
    print(f"\nMonthly Distribution in Top {top_n}:")
    for month, count in monthly_dist.items():
        pct = (count / len(top_entries) * 100)
        print(f"  {month}: {count} entries ({pct:.1f}%)")
    
    # Brand distribution
    brand_dist = top_entries['ABI SKU'].str.extract(r'^([A-Z]+)')[0].value_counts()
    print(f"\nBrand Distribution in Top {top_n}:")
    for brand, count in brand_dist.items():
        pct = (count / len(top_entries) * 100)
        print(f"  {brand}: {count} entries ({pct:.1f}%)")
    
    # Show actual top entries with dates
    print(f"\nTop 10 Highest Uplift Entries:")
    top_10 = top_entries[['ABI Start', 'ABI MS Promo Uplift - rel', 'KSM', 'Start_Month_Name', 'ABI SKU']].head(10)
    for idx, row in top_10.iterrows():
        print(f"  {row['ABI Start'].strftime('%Y-%m-%d')} | Uplift: {row['ABI MS Promo Uplift - rel']:.3f} | KSM: {row['KSM']} | {row['Start_Month_Name']} | {row['ABI SKU']}")
    
    return top_entries

def create_visualizations(df, results):
    """Create visualizations for the analysis"""
    print("\n" + "="*60)
    print("CREATING VISUALIZATIONS")
    print("="*60)
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. KSM vs Uplift boxplot
    sns.boxplot(data=df, x='KSM', y='ABI MS Promo Uplift - rel', ax=axes[0,0])
    axes[0,0].set_title('KSM vs Uplift Distribution')
    axes[0,0].set_ylabel('ABI MS Promo Uplift - rel')
    
    # 2. Monthly pattern
    monthly_stats = df.groupby('Start_Month_Name')['ABI MS Promo Uplift - rel'].mean().reindex([
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ])
    
    axes[0,1].bar(range(len(monthly_stats)), monthly_stats.values)
    axes[0,1].set_title('Average Uplift by Month')
    axes[0,1].set_xlabel('Month')
    axes[0,1].set_ylabel('Average Uplift')
    axes[0,1].set_xticks(range(len(monthly_stats)))
    axes[0,1].set_xticklabels(monthly_stats.index, rotation=45)
    
    # 3. KSM distribution by month
    ksm_monthly = df.groupby(['Start_Month_Name', 'KSM']).size().unstack(fill_value=0)
    ksm_monthly.plot(kind='bar', ax=axes[1,0])
    axes[1,0].set_title('KSM Distribution by Month')
    axes[1,0].set_xlabel('Month')
    axes[1,0].set_ylabel('Count')
    axes[1,0].legend(title='KSM', labels=['KSM=0', 'KSM=1'])
    
    # 4. Top percentile KSM comparison
    top_20_threshold = np.percentile(df['ABI MS Promo Uplift - rel'], 80)
    top_20_df = df[df['ABI MS Promo Uplift - rel'] >= top_20_threshold]
    
    ksm_comparison = [
        df[df['KSM'] == 0]['ABI MS Promo Uplift - rel'].mean(),
        df[df['KSM'] == 1]['ABI MS Promo Uplift - rel'].mean(),
        top_20_df[top_20_df['KSM'] == 0]['ABI MS Promo Uplift - rel'].mean(),
        top_20_df[top_20_df['KSM'] == 1]['ABI MS Promo Uplift - rel'].mean()
    ]
    
    labels = ['All KSM=0', 'All KSM=1', 'Top20% KSM=0', 'Top20% KSM=1']
    colors = ['lightblue', 'lightcoral', 'blue', 'red']
    
    axes[1,1].bar(labels, ksm_comparison, color=colors)
    axes[1,1].set_title('KSM Impact: All vs Top 20%')
    axes[1,1].set_ylabel('Average Uplift')
    axes[1,1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('ksm_uplift_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ Visualizations saved as 'ksm_uplift_analysis.png'")

def main():
    """Main analysis function"""
    print("KSM UPLIFT ANALYSIS")
    print("="*60)
    
    # Load data
    df = load_data()
    
    # Analyze KSM coefficients
    ksm_stats = analyze_ksm_coefficients(df)
    
    # Filter top percentile uplifts
    percentile_results = filter_top_percentile_uplifts(df)
    
    # Analyze seasonal patterns
    top_20_df, monthly_uplift = analyze_seasonal_patterns(df)
    
    # Analyze KSM impact by month
    ksm_impact = analyze_ksm_impact_by_month(df)
    
    # Identify highest uplift patterns
    top_entries = identify_highest_uplift_patterns(df)
    
    # Create visualizations
    create_visualizations(df, percentile_results)
    
    return {
        'ksm_stats': ksm_stats,
        'percentile_results': percentile_results,
        'top_20_df': top_20_df,
        'monthly_uplift': monthly_uplift,
        'ksm_impact': ksm_impact,
        'top_entries': top_entries
    }

if __name__ == "__main__":
    results = main()
    print("\n" + "="*60)
    print("ANALYSIS COMPLETE!")
    print("="*60)