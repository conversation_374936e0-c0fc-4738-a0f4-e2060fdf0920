#!/usr/bin/env python3
"""
Detailed KSM Pattern Analysis
Investigates specific dates, festivals, and patterns in high uplift periods
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load and prepare data"""
    df = pd.read_csv('demelted_data.csv')
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    df['Start_Month'] = df['ABI Start'].dt.month
    df['Start_Month_Name'] = df['ABI Start'].dt.strftime('%B')
    df['Start_Day'] = df['ABI Start'].dt.day
    df['Start_Year'] = df['ABI Start'].dt.year
    df['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    df = df.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])
    return df

def analyze_top_uplift_dates(df, top_n=30):
    """Analyze the top N highest uplift dates in detail"""
    print(f"\n" + "="*80)
    print(f"TOP {top_n} HIGHEST UPLIFT DATES ANALYSIS")
    print("="*80)
    
    top_uplifts = df.nlargest(top_n, 'ABI_MS_Uplift_Rel_Raw')
    
    print(f"Detailed breakdown of top {top_n} uplifts:")
    print(f"{'Rank':<4} {'Date':<12} {'Uplift':<8} {'KSM':<3} {'Month':<10} {'Brand':<15} {'Retailer':<25}")
    print("-" * 80)
    
    for i, (idx, row) in enumerate(top_uplifts.iterrows(), 1):
        date_str = row['ABI Start'].strftime('%Y-%m-%d')
        uplift = row['ABI_MS_Uplift_Rel_Raw']
        ksm = row['KSM']
        month = row['Start_Month_Name']
        brand = str(row['ABI SKU']).split()[0] if pd.notna(row['ABI SKU']) else 'Unknown'
        retailer = str(row['Retailer'])[:24] if pd.notna(row['Retailer']) else 'Unknown'
        
        print(f"{i:<4} {date_str:<12} {uplift:<8.3f} {ksm:<3} {month:<10} {brand:<15} {retailer:<25}")
    
    # Monthly distribution of top uplifts
    monthly_dist = top_uplifts['Start_Month_Name'].value_counts()
    print(f"\nMonthly distribution of top {top_n} uplifts:")
    for month, count in monthly_dist.items():
        pct = count / top_n * 100
        ksm_count = top_uplifts[top_uplifts['Start_Month_Name'] == month]['KSM'].sum()
        print(f"  {month:12}: {count:2d} entries ({pct:4.1f}%), KSM: {ksm_count}")
    
    return top_uplifts

def analyze_seasonal_festivals(df):
    """Analyze uplift patterns around potential festival periods"""
    print(f"\n" + "="*80)
    print("SEASONAL FESTIVAL ANALYSIS")
    print("="*80)
    
    # Define potential festival periods (approximate dates)
    festival_periods = {
        'New Year': [(1, 1, 15)],  # January 1-15
        'Easter': [(3, 15, 4, 15)],  # Mid March to Mid April
        'Summer': [(6, 1, 8, 31)],  # June-August
        'Back to School': [(8, 15, 9, 15)],  # Mid Aug to Mid Sep
        'Halloween': [(10, 15, 10, 31)],  # Mid to End October
        'Christmas': [(12, 1, 12, 31)],  # December
    }
    
    # Add festival period classification
    def classify_festival_period(row):
        month = row['Start_Month']
        day = row['Start_Day']
        
        if month == 1 and day <= 15:
            return 'New Year'
        elif (month == 3 and day >= 15) or (month == 4 and day <= 15):
            return 'Easter'
        elif month in [6, 7, 8]:
            return 'Summer'
        elif (month == 8 and day >= 15) or (month == 9 and day <= 15):
            return 'Back to School'
        elif month == 10 and day >= 15:
            return 'Halloween'
        elif month == 12:
            return 'Christmas'
        else:
            return 'Regular'
    
    df['Festival_Period'] = df.apply(classify_festival_period, axis=1)
    
    # Analyze uplift by festival period
    festival_stats = df.groupby('Festival_Period').agg({
        'ABI_MS_Uplift_Rel_Raw': ['count', 'mean', 'std'],
        'KSM': ['sum', 'mean']
    }).round(3)
    
    print("Uplift performance by festival period:")
    print(f"{'Period':<15} {'Count':<6} {'Mean Uplift':<12} {'Std':<8} {'KSM Count':<10} {'KSM Rate':<10}")
    print("-" * 70)
    
    for period in festival_stats.index:
        count = festival_stats.loc[period, ('ABI_MS_Uplift_Rel_Raw', 'count')]
        mean_uplift = festival_stats.loc[period, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
        std_uplift = festival_stats.loc[period, ('ABI_MS_Uplift_Rel_Raw', 'std')]
        ksm_count = festival_stats.loc[period, ('KSM', 'sum')]
        ksm_rate = festival_stats.loc[period, ('KSM', 'mean')]
        
        print(f"{period:<15} {count:<6} {mean_uplift:<12.3f} {std_uplift:<8.3f} {ksm_count:<10} {ksm_rate:<10.3f}")
    
    return festival_stats

def investigate_high_uplift_months(df):
    """Deep dive into months with highest uplift concentration"""
    print(f"\n" + "="*80)
    print("HIGH UPLIFT MONTHS INVESTIGATION")
    print("="*80)
    
    # Focus on top 20% uplifts
    threshold_80 = np.percentile(df['ABI_MS_Uplift_Rel_Raw'], 80)
    high_uplift_df = df[df['ABI_MS_Uplift_Rel_Raw'] >= threshold_80]
    
    # Top months by high uplift count
    top_months = ['October', 'August', 'March', 'February', 'September']
    
    for month in top_months:
        month_data = high_uplift_df[high_uplift_df['Start_Month_Name'] == month]
        
        if len(month_data) == 0:
            continue
            
        print(f"\n--- {month.upper()} ANALYSIS ---")
        print(f"High uplift entries: {len(month_data)}")
        print(f"Average uplift: {month_data['ABI_MS_Uplift_Rel_Raw'].mean():.3f}")
        print(f"KSM entries: {month_data['KSM'].sum()}")
        print(f"KSM rate: {month_data['KSM'].mean():.3f}")
        
        # Top dates in this month
        top_dates = month_data.nlargest(5, 'ABI_MS_Uplift_Rel_Raw')
        print(f"\nTop 5 dates in {month}:")
        for idx, row in top_dates.iterrows():
            date_str = row['ABI Start'].strftime('%Y-%m-%d')
            uplift = row['ABI_MS_Uplift_Rel_Raw']
            ksm = row['KSM']
            brand = str(row['ABI SKU']).split()[0] if pd.notna(row['ABI SKU']) else 'Unknown'
            print(f"  {date_str}: {uplift:.3f} (KSM: {ksm}) - {brand}")
        
        # Year-over-year pattern
        yearly_pattern = month_data.groupby('Start_Year').agg({
            'ABI_MS_Uplift_Rel_Raw': ['count', 'mean'],
            'KSM': 'sum'
        }).round(3)
        
        if len(yearly_pattern) > 1:
            print(f"\nYear-over-year pattern in {month}:")
            for year in yearly_pattern.index:
                count = yearly_pattern.loc[year, ('ABI_MS_Uplift_Rel_Raw', 'count')]
                mean_uplift = yearly_pattern.loc[year, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
                ksm_count = yearly_pattern.loc[year, ('KSM', 'sum')]
                print(f"  {year}: {count} entries, avg uplift: {mean_uplift:.3f}, KSM: {ksm_count}")

def analyze_ksm_vs_non_ksm_patterns(df):
    """Compare patterns between KSM and non-KSM periods"""
    print(f"\n" + "="*80)
    print("KSM vs NON-KSM PATTERN COMPARISON")
    print("="*80)
    
    ksm_data = df[df['KSM'] == 1]
    non_ksm_data = df[df['KSM'] == 0]
    
    print(f"KSM periods: {len(ksm_data)} entries")
    print(f"Non-KSM periods: {len(non_ksm_data)} entries")
    
    # Monthly distribution comparison
    ksm_monthly = ksm_data['Start_Month_Name'].value_counts()
    non_ksm_monthly = non_ksm_data['Start_Month_Name'].value_counts()
    
    print(f"\nMonthly distribution comparison:")
    print(f"{'Month':<12} {'KSM Count':<10} {'Non-KSM Count':<15} {'KSM Rate':<10}")
    print("-" * 50)
    
    all_months = sorted(set(ksm_monthly.index) | set(non_ksm_monthly.index))
    for month in all_months:
        ksm_count = ksm_monthly.get(month, 0)
        non_ksm_count = non_ksm_monthly.get(month, 0)
        total = ksm_count + non_ksm_count
        ksm_rate = ksm_count / total if total > 0 else 0
        
        print(f"{month:<12} {ksm_count:<10} {non_ksm_count:<15} {ksm_rate:<10.3f}")
    
    # Uplift performance comparison
    print(f"\nUplift performance comparison:")
    print(f"KSM periods - Mean: {ksm_data['ABI_MS_Uplift_Rel_Raw'].mean():.3f}, Std: {ksm_data['ABI_MS_Uplift_Rel_Raw'].std():.3f}")
    print(f"Non-KSM periods - Mean: {non_ksm_data['ABI_MS_Uplift_Rel_Raw'].mean():.3f}, Std: {non_ksm_data['ABI_MS_Uplift_Rel_Raw'].std():.3f}")

def main():
    """Main analysis function"""
    print("DETAILED KSM PATTERN ANALYSIS")
    print("="*80)
    
    # Load data
    df = load_data()
    print(f"Loaded {len(df)} promotion records")
    print(f"Date range: {df['ABI Start'].min().strftime('%Y-%m-%d')} to {df['ABI Start'].max().strftime('%Y-%m-%d')}")
    
    # Analyze top uplift dates
    top_uplifts = analyze_top_uplift_dates(df, 30)
    
    # Analyze seasonal festivals
    festival_stats = analyze_seasonal_festivals(df)
    
    # Investigate high uplift months
    investigate_high_uplift_months(df)
    
    # Compare KSM vs non-KSM patterns
    analyze_ksm_vs_non_ksm_patterns(df)
    
    print(f"\n" + "="*80)
    print("ANALYSIS COMPLETED")
    print("="*80)

if __name__ == "__main__":
    main()
