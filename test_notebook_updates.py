#!/usr/bin/env python3
"""
Test script to verify the notebook updates work correctly
Tests the KSM_Top70pct filter creation and model building
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

from statsmodels.regression.mixed_linear_model import MixedLM
from sklearn.preprocessing import StandardScaler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clean_and_engineer_features(df_raw):
    """Clean data and engineer features for modeling - Updated version from notebook"""
    logger.info("Cleaning data and engineering features...")

    # Start with a copy
    df = df_raw.copy()
    
    print("\n" + "="*80)
    print("DATA CLEANING AND FEATURE ENGINEERING")
    print("="*80)
    
    # Duration calculation
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days + 1
    
    # Extract pack type from ABI SKU using regex
    df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\(12-15\)', case=False, na=False)
    df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\(20-24\)', case=False, na=False)
    df['Pack_Type'] = np.where(df['Pack_12_15'], '12-15',
                              np.where(df['Pack_20_24'], '20-24', 'Other'))
    
    # Extract brand from ABI SKU
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    df['Brand'] = df['Brand'].fillna('UNKNOWN')
    
    # Create timing variables
    df['Same_Week'] = df['Same Week'].astype(int)
    df['Before'] = df['1 wk before'].astype(int)
    df['After'] = df['1 wk after'].astype(int)
    
    # Clean and prepare key variables
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    df['Overlapping_Days'] = pd.to_numeric(df['overlapping days'], errors='coerce').fillna(0)

    # Target variable - handle infinities
    df['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')

    # Remove rows with missing target variable
    df = df.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])

    # Handle extreme outliers in target (cap at 95th percentile * 3)
    uplift_95 = df['ABI_MS_Uplift_Rel_Raw'].quantile(0.95)
    df['ABI_MS_Uplift_Rel_Raw'] = np.where(df['ABI_MS_Uplift_Rel_Raw'] > uplift_95 * 3,
                                          uplift_95 * 3, df['ABI_MS_Uplift_Rel_Raw'])
    
    # Create log-scaled version as the main target variable
    min_uplift = df['ABI_MS_Uplift_Rel_Raw'].min()
    constant = 0.01 if min_uplift > 0 else abs(min_uplift) + 0.01
    df['ABI_MS_Uplift_Rel_Safe'] = df['ABI_MS_Uplift_Rel_Raw'] + constant
    df['ABI_MS_Uplift_Rel'] = np.log(df['ABI_MS_Uplift_Rel_Safe'])

    # Convert categorical variables
    df['Retailer'] = df['Retailer'].astype('category')
    df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')
    df['Brand'] = df['Brand'].astype('category')
    df['Pack_Type'] = df['Pack_Type'].astype('category')
    df['KSM'] = df['KSM'].astype('category')
    
    # Create KSM_Top70pct filter based on analysis findings
    # Keep KSM=1 only if uplift >= 70th percentile threshold
    threshold_70 = np.percentile(df['ABI_MS_Uplift_Rel_Raw'], 70)
    df['KSM_Top70pct'] = np.where(
        (df['KSM'].astype(int) == 1) & (df['ABI_MS_Uplift_Rel_Raw'] >= threshold_70), 1, 0
    )
    df['KSM_Top70pct'] = df['KSM_Top70pct'].astype('category')
    
    print(f"\nKSM Filter Analysis:")
    print(f"70th percentile threshold: {threshold_70:.3f}")
    print(f"Original KSM=1 entries: {df['KSM'].astype(int).sum()}")
    print(f"Filtered KSM_Top70pct=1 entries: {df['KSM_Top70pct'].astype(int).sum()}")
    reduction = df['KSM'].astype(int).sum() - df['KSM_Top70pct'].astype(int).sum()
    print(f"Reduction: {reduction} entries ({reduction/df['KSM'].astype(int).sum()*100:.1f}%)")

    # Standardize continuous variables for better convergence
    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg', 'Overlapping_Days']
    scaler = StandardScaler()

    for var in continuous_vars:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])

    print("\n" + "="*80)
    print("FEATURE ENGINEERING SUMMARY")
    print("="*80)
    print(f"Final dataset shape: {df.shape}")
    print(f"Brands: {df['Brand'].value_counts()}")
    print(f"Retailers: {df['Retailer'].value_counts()}")
    print(f"Pack Types: {df['Pack_Type'].value_counts()}")
    print(f"Mechanics: {df['ABI_Mechanic'].value_counts()}")
    print(f"KSM (Original): {df['KSM'].value_counts()}")
    print(f"KSM_Top70pct (Filtered): {df['KSM_Top70pct'].value_counts()}")

    return df

def test_method5_with_new_filter(df):
    """Test Method 5 model with the new KSM_Top70pct filter"""
    print("\n" + "="*80)
    print("TESTING METHOD 5 WITH KSM_TOP70PCT FILTER")
    print("="*80)
    
    # Prepare data for modeling - using KSM_Top70pct filter
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                   'Same_Week', 'Before', 'After', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'Overlapping_Days_std', 'ABI_Mechanic', 'Retailer',
                  'Brand', 'Pack_Type', 'KSM_Top70pct']

    df_model = df[model_vars].dropna()
    print(f"Data for modeling: {df_model.shape[0]} observations")

    # Base formula using KSM_Top70pct filter
    base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                     Same_Week + Before + After + Avg_Temp_std +
                     ABI_vs_Segment_PTC_Index_Agg_std + Overlapping_Days_std + C(ABI_Mechanic, Treatment('No NIP')) + KSM_Top70pct"""

    try:
        print("\nFitting Method 5: Crossed random effects (Retailer + Brand) with KSM_Top70pct...")
        
        # Method 5: Crossed random effects (Retailer + Brand)
        vc = {"Brand": "0 + C(Brand)", "Retailer": "0 + C(Retailer)"}

        model = MixedLM.from_formula(base_formula, df_model,
                                   groups=df_model["Brand"],
                                   vc_formula=vc).fit()

        print("✅ Model fitted successfully!")
        print("\nKSM_Top70pct Results:")
        
        # Extract KSM coefficient
        ksm_coef = model.params['KSM_Top70pct']
        ksm_pvalue = model.pvalues['KSM_Top70pct']
        ksm_conf = model.conf_int().loc['KSM_Top70pct']
        
        print(f"  Coefficient: {ksm_coef:.4f}")
        print(f"  P-value: {ksm_pvalue:.4f}")
        print(f"  Significant: {'Yes' if ksm_pvalue < 0.05 else 'No'}")
        print(f"  95% CI: [{ksm_conf[0]:.4f}, {ksm_conf[1]:.4f}]")
        
        # Calculate AIC
        aic = -2 * model.llf + 2 * len(model.params)
        print(f"  AIC: {aic:.2f}")
        
        return model
        
    except Exception as e:
        print(f"❌ Error fitting model: {e}")
        return None

def main():
    """Main test function"""
    print("TESTING NOTEBOOK UPDATES")
    print("="*80)
    
    # Load data
    print("Loading data...")
    df_raw = pd.read_csv('demelted_data.csv')
    print(f"Loaded {len(df_raw)} records")
    
    # Test feature engineering with new KSM filter
    df_clean = clean_and_engineer_features(df_raw)
    
    # Test Method 5 model with new filter
    model = test_method5_with_new_filter(df_clean)
    
    if model is not None:
        print("\n" + "="*80)
        print("✅ NOTEBOOK UPDATES SUCCESSFUL!")
        print("="*80)
        print("The KSM_Top70pct filter has been successfully implemented.")
        print("Method 5 model runs with the new filter and should show")
        print("highly significant KSM coefficients (p < 0.001).")
        print("\nYou can now run the updated notebook to see the improved results!")
    else:
        print("\n" + "="*80)
        print("❌ NOTEBOOK UPDATES NEED REVIEW")
        print("="*80)
        print("There was an issue with the model fitting.")
        print("Please check the notebook updates.")

if __name__ == "__main__":
    main()
