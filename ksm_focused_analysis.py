#!/usr/bin/env python3
"""
Focused KSM Analysis Script
Tests different KSM percentile filters with Method 5 hierarchical model
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from statsmodels.regression.mixed_linear_model import MixedLM
from sklearn.preprocessing import StandardScaler

def load_and_prepare_data():
    """Load and prepare data for modeling"""
    print("Loading and preparing data...")
    df = pd.read_csv('demelted_data.csv')
    
    # Convert dates and extract features
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days + 1
    df['Start_Month_Name'] = df['ABI Start'].dt.strftime('%B')
    
    # Extract brand and pack type
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0].fillna('UNKNOWN')
    df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\(12-15\)', case=False, na=False)
    df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\(20-24\)', case=False, na=False)
    df['Pack_Type'] = np.where(df['Pack_12_15'], '12-15',
                              np.where(df['Pack_20_24'], '20-24', 'Other'))
    
    # Create timing variables
    df['Same_Week'] = df['Same Week'].astype(int)
    df['Before'] = df['1 wk before'].astype(int)
    df['After'] = df['1 wk after'].astype(int)
    
    # Clean numeric variables
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    df['Overlapping_Days'] = pd.to_numeric(df['overlapping days'], errors='coerce').fillna(0)
    
    # Target variable processing
    df['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    df = df.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])
    
    # Handle outliers (cap at 95th percentile * 3)
    uplift_95 = df['ABI_MS_Uplift_Rel_Raw'].quantile(0.95)
    df['ABI_MS_Uplift_Rel_Raw'] = np.where(df['ABI_MS_Uplift_Rel_Raw'] > uplift_95 * 3,
                                          uplift_95 * 3, df['ABI_MS_Uplift_Rel_Raw'])
    
    # Log transform target
    min_uplift = df['ABI_MS_Uplift_Rel_Raw'].min()
    constant = 0.01 if min_uplift > 0 else abs(min_uplift) + 0.01
    df['ABI_MS_Uplift_Rel'] = np.log(df['ABI_MS_Uplift_Rel_Raw'] + constant)
    
    # Convert categoricals
    df['Retailer'] = df['Retailer'].astype('category')
    df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')
    df['Brand'] = df['Brand'].astype('category')
    df['KSM'] = df['KSM'].astype(int)
    
    # Standardize continuous variables
    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg', 'Overlapping_Days']
    scaler = StandardScaler()
    
    for var in continuous_vars:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    print(f"Data prepared: {df.shape[0]} observations")
    return df

def create_ksm_filters(df):
    """Create KSM filters based on uplift percentiles"""
    print("\nCreating KSM percentile filters...")
    
    uplift_col = 'ABI_MS_Uplift_Rel_Raw'
    percentiles_to_test = [10, 20, 25, 30, 40]
    
    original_ksm_count = df['KSM'].sum()
    print(f"Original KSM=1 entries: {original_ksm_count}")
    
    filters = {}
    
    for p in percentiles_to_test:
        threshold = np.percentile(df[uplift_col], p)
        
        # Create filter: Keep KSM=1 only if uplift >= percentile threshold
        filter_name = f'KSM_Top{100-p}pct'
        df[filter_name] = np.where((df['KSM'] == 1) & (df[uplift_col] >= threshold), 1, 0)
        
        filtered_count = df[filter_name].sum()
        reduction = original_ksm_count - filtered_count
        
        print(f"  {filter_name}: {filtered_count} entries (reduced by {reduction}, {reduction/original_ksm_count*100:.1f}%)")
        filters[p] = filter_name
    
    return filters

def run_method5_model(df, ksm_column):
    """Run Method 5 (Brand + Retailer Crossed Random Effects) with specific KSM column"""
    
    # Prepare modeling data
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                  'Same_Week', 'Before', 'After', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'Overlapping_Days_std', 
                  'ABI_Mechanic', 'Retailer', 'Brand', ksm_column]
    
    df_model = df[model_vars].dropna()
    
    # Formula
    formula = f"""ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                  Same_Week + Before + After + Avg_Temp_std +
                  ABI_vs_Segment_PTC_Index_Agg_std + Overlapping_Days_std + 
                  C(ABI_Mechanic, Treatment('No NIP')) + {ksm_column}"""
    
    try:
        # Method 5: Crossed random effects (Brand + Retailer)
        vc = {"Brand": "0 + C(Brand)", "Retailer": "0 + C(Retailer)"}
        
        model = MixedLM.from_formula(formula, df_model,
                                   groups=df_model["Brand"],
                                   vc_formula=vc).fit()
        
        return model, df_model
    
    except Exception as e:
        print(f"    Error fitting model: {e}")
        return None, df_model

def extract_ksm_results(model, ksm_column):
    """Extract KSM coefficient results from model"""
    if model is None:
        return None
    
    try:
        coef = model.params[ksm_column]
        pvalue = model.pvalues[ksm_column]
        conf_int = model.conf_int().loc[ksm_column]
        
        # Calculate AIC
        aic = -2 * model.llf + 2 * len(model.params)
        
        return {
            'coefficient': coef,
            'pvalue': pvalue,
            'conf_lower': conf_int[0],
            'conf_upper': conf_int[1],
            'aic': aic,
            'significant': pvalue < 0.05
        }
    
    except Exception as e:
        print(f"    Error extracting results: {e}")
        return None

def analyze_monthly_patterns(df):
    """Analyze monthly patterns in high uplifts"""
    print("\nAnalyzing monthly patterns...")
    
    # Focus on top 20% uplifts
    threshold_80 = np.percentile(df['ABI_MS_Uplift_Rel_Raw'], 80)
    top_20_df = df[df['ABI_MS_Uplift_Rel_Raw'] >= threshold_80]
    
    monthly_stats = top_20_df.groupby('Start_Month_Name').agg({
        'ABI_MS_Uplift_Rel_Raw': ['count', 'mean'],
        'KSM': 'sum'
    }).round(3)
    
    print(f"\nTop 20% uplifts by month ({len(top_20_df)} total entries):")
    
    # Sort by count descending
    monthly_sorted = monthly_stats.sort_values(('ABI_MS_Uplift_Rel_Raw', 'count'), ascending=False)
    
    patterns = {}
    for month in monthly_sorted.index:
        count = monthly_sorted.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'count')]
        mean_uplift = monthly_sorted.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
        ksm_count = monthly_sorted.loc[month, ('KSM', 'sum')]
        pct = count / len(top_20_df) * 100
        
        print(f"  {month:12}: {count:2d} entries ({pct:4.1f}%), avg: {mean_uplift:.3f}, KSM: {ksm_count}")
        
        patterns[month] = {
            'count': count,
            'percentage': pct,
            'mean_uplift': mean_uplift,
            'ksm_count': ksm_count
        }
    
    return patterns

def main_analysis():
    """Main analysis function"""
    print("KSM FOCUSED ANALYSIS")
    print("="*60)
    
    # Load data
    df = load_and_prepare_data()
    
    # Create KSM filters
    ksm_filters = create_ksm_filters(df)
    
    # Run models
    print(f"\n" + "="*60)
    print("RUNNING METHOD 5 MODELS WITH DIFFERENT KSM FILTERS")
    print("="*60)
    
    results = {}
    
    # Test original KSM
    print(f"\n--- ORIGINAL KSM ---")
    model, df_model = run_method5_model(df, 'KSM')
    result = extract_ksm_results(model, 'KSM')
    
    if result:
        print(f"  Coefficient: {result['coefficient']:7.4f}")
        print(f"  P-value:     {result['pvalue']:7.4f}")
        print(f"  Significant: {'Yes' if result['significant'] else 'No'}")
        print(f"  95% CI:      [{result['conf_lower']:6.4f}, {result['conf_upper']:6.4f}]")
        print(f"  AIC:         {result['aic']:7.2f}")
    
    results['original'] = result
    
    # Test each percentile filter
    for percentile, ksm_column in ksm_filters.items():
        print(f"\n--- {ksm_column.upper()} ---")
        
        model, df_model = run_method5_model(df, ksm_column)
        result = extract_ksm_results(model, ksm_column)
        
        if result:
            print(f"  Coefficient: {result['coefficient']:7.4f}")
            print(f"  P-value:     {result['pvalue']:7.4f}")
            print(f"  Significant: {'Yes' if result['significant'] else 'No'}")
            print(f"  95% CI:      [{result['conf_lower']:6.4f}, {result['conf_upper']:6.4f}]")
            print(f"  AIC:         {result['aic']:7.2f}")
        
        results[percentile] = result
    
    # Analyze monthly patterns
    monthly_patterns = analyze_monthly_patterns(df)
    
    return results, monthly_patterns, df

if __name__ == "__main__":
    results, patterns, df = main_analysis()
