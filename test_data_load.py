#!/usr/bin/env python3
"""
Test script to check data loading and basic analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_data_load():
    """Test loading the data file"""
    try:
        print("Testing data load...")
        df = pd.read_csv('demelted_data.csv')
        print(f"Data loaded successfully: {df.shape[0]} rows, {df.shape[1]} columns")
        
        # Check key columns
        print(f"\nKey columns present:")
        key_cols = ['ABI Start', 'ABI MS Promo Uplift - rel', 'KSM', 'Retailer', 'ABI SKU']
        for col in key_cols:
            if col in df.columns:
                print(f"  ✓ {col}")
            else:
                print(f"  ✗ {col} - MISSING")
        
        # Basic statistics
        print(f"\nBasic statistics:")
        print(f"  ABI MS Promo Uplift - rel: mean={df['ABI MS Promo Uplift - rel'].mean():.3f}, std={df['ABI MS Promo Uplift - rel'].std():.3f}")
        print(f"  KSM distribution: {df['KSM'].value_counts().to_dict()}")
        
        # Date range
        df['ABI Start'] = pd.to_datetime(df['ABI Start'])
        print(f"  Date range: {df['ABI Start'].min()} to {df['ABI Start'].max()}")
        
        return df
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def test_percentile_analysis(df):
    """Test percentile analysis"""
    if df is None:
        return
    
    print(f"\n" + "="*50)
    print("PERCENTILE ANALYSIS TEST")
    print("="*50)
    
    uplift_col = 'ABI MS Promo Uplift - rel'
    
    # Calculate percentiles
    percentiles = [10, 20, 25, 30, 40, 50, 70, 80, 90]
    
    print(f"Uplift percentiles:")
    for p in percentiles:
        value = np.percentile(df[uplift_col], p)
        count_above = len(df[df[uplift_col] >= value])
        print(f"  {p:2d}th: {value:6.3f} ({count_above:3d} entries above)")
    
    # Test KSM filtering
    print(f"\nKSM filtering test:")
    original_ksm = df['KSM'].sum()
    print(f"  Original KSM=1 entries: {original_ksm}")
    
    for p in [20, 30, 40]:
        threshold = np.percentile(df[uplift_col], p)
        filtered_ksm = ((df['KSM'] == 1) & (df[uplift_col] >= threshold)).sum()
        reduction = original_ksm - filtered_ksm
        print(f"  Top {100-p}% filter (≥{threshold:.3f}): {filtered_ksm} KSM entries (reduced by {reduction})")

def test_monthly_patterns(df):
    """Test monthly pattern analysis"""
    if df is None:
        return
    
    print(f"\n" + "="*50)
    print("MONTHLY PATTERN TEST")
    print("="*50)
    
    df['Start_Month_Name'] = df['ABI Start'].dt.strftime('%B')
    
    # Top 20% uplifts by month
    threshold_80 = np.percentile(df['ABI MS Promo Uplift - rel'], 80)
    top_20_df = df[df['ABI MS Promo Uplift - rel'] >= threshold_80]
    
    print(f"Top 20% uplifts (≥{threshold_80:.3f}): {len(top_20_df)} entries")
    
    monthly_stats = top_20_df.groupby('Start_Month_Name').agg({
        'ABI MS Promo Uplift - rel': ['count', 'mean'],
        'KSM': 'sum'
    }).round(3)
    
    print(f"\nMonthly distribution of top 20% uplifts:")
    for month in monthly_stats.index:
        count = monthly_stats.loc[month, ('ABI MS Promo Uplift - rel', 'count')]
        mean_uplift = monthly_stats.loc[month, ('ABI MS Promo Uplift - rel', 'mean')]
        ksm_count = monthly_stats.loc[month, ('KSM', 'sum')]
        pct = count / len(top_20_df) * 100
        print(f"  {month:12}: {count:3d} entries ({pct:4.1f}%), avg: {mean_uplift:.3f}, KSM: {ksm_count}")

if __name__ == "__main__":
    print("DATA LOADING AND ANALYSIS TEST")
    print("="*50)
    
    # Test data loading
    df = test_data_load()
    
    # Test percentile analysis
    test_percentile_analysis(df)
    
    # Test monthly patterns
    test_monthly_patterns(df)
    
    print(f"\n" + "="*50)
    print("TEST COMPLETED")
    print("="*50)
