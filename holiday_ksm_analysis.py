#!/usr/bin/env python3
"""
Holiday KSM Analysis
Analyzes which specific holidays correlate with top 70 percentile uplifts,
considering the 1-week before window for promotional timing.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load promotion and holiday data"""
    print("Loading data...")
    
    # Load promotion data
    df_promo = pd.read_csv('demelted_data.csv')
    df_promo['ABI Start'] = pd.to_datetime(df_promo['ABI Start'])
    df_promo['ABI End'] = pd.to_datetime(df_promo['ABI End'])
    df_promo['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df_promo['ABI MS Promo Uplift - rel'], errors='coerce')
    df_promo = df_promo.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])
    
    # Load holiday data
    df_holidays = pd.read_csv('Holidays.csv')
    df_holidays['date'] = pd.to_datetime(df_holidays['date']).dt.tz_localize(None)  # Remove timezone
    df_holidays['year'] = df_holidays['date'].dt.year
    
    print(f"Loaded {len(df_promo)} promotion records")
    print(f"Loaded {len(df_holidays)} holiday records")
    print(f"Holiday date range: {df_holidays['date'].min()} to {df_holidays['date'].max()}")
    print(f"Promotion date range: {df_promo['ABI Start'].min()} to {df_promo['ABI Start'].max()}")
    
    return df_promo, df_holidays

def identify_top_70_percentile(df_promo):
    """Identify top 70 percentile uplifts"""
    threshold_70 = np.percentile(df_promo['ABI_MS_Uplift_Rel_Raw'], 70)
    top_70_df = df_promo[df_promo['ABI_MS_Uplift_Rel_Raw'] >= threshold_70].copy()
    
    print(f"\nTop 70 percentile analysis:")
    print(f"Threshold: {threshold_70:.3f}")
    print(f"Number of top 70% entries: {len(top_70_df)}")
    
    return top_70_df, threshold_70

def match_promotions_to_holidays(df_promo, df_holidays, days_before=7):
    """
    Match promotions to holidays considering promotional timing
    (promotions typically start 1 week before the actual holiday)
    """
    print(f"\nMatching promotions to holidays (considering {days_before} days before holiday)...")
    
    # Create a list to store matches
    matches = []
    
    for idx, promo in df_promo.iterrows():
        promo_start = promo['ABI Start']
        promo_end = promo['ABI End']
        
        # Find holidays that fall within the promotional period or up to days_before after promo start
        for hidx, holiday in df_holidays.iterrows():
            holiday_date = holiday['date']
            
            # Check if promotion starts within days_before of the holiday
            # (promotion starts before holiday, targeting the holiday)
            days_diff = (holiday_date - promo_start).days
            
            if 0 <= days_diff <= days_before:
                matches.append({
                    'promo_id': idx,
                    'ABI Start': promo_start,
                    'ABI End': promo_end,
                    'ABI_MS_Uplift_Rel_Raw': promo['ABI_MS_Uplift_Rel_Raw'],
                    'KSM': promo['KSM'],
                    'Brand': promo.get('ABI SKU', '').split()[0] if pd.notna(promo.get('ABI SKU')) else 'Unknown',
                    'Retailer': promo['Retailer'],
                    'holiday_date': holiday_date,
                    'holiday_fr': holiday['bank_holiday_fr'],
                    'holiday_uk': holiday['bank_holiday_uk'],
                    'days_before_holiday': days_diff,
                    'year': holiday['annee']
                })
    
    matches_df = pd.DataFrame(matches)
    print(f"Found {len(matches_df)} promotion-holiday matches")
    
    return matches_df

def analyze_holiday_performance(matches_df, top_70_df):
    """Analyze which holidays have the best uplift performance"""
    print(f"\n" + "="*80)
    print("HOLIDAY PERFORMANCE ANALYSIS")
    print("="*80)
    
    if len(matches_df) == 0:
        print("No holiday matches found!")
        return None
    
    # Filter matches to only top 70 percentile uplifts
    top_70_ids = set(top_70_df.index)
    top_matches = matches_df[matches_df['promo_id'].isin(top_70_ids)].copy()
    
    print(f"Total holiday-matched promotions: {len(matches_df)}")
    print(f"Top 70% holiday-matched promotions: {len(top_matches)}")
    
    if len(top_matches) == 0:
        print("No top 70% promotions match with holidays!")
        return None
    
    # Analyze by French holidays (more relevant for the market)
    holiday_performance = top_matches.groupby('holiday_fr').agg({
        'ABI_MS_Uplift_Rel_Raw': ['count', 'mean', 'std', 'min', 'max'],
        'KSM': 'sum',
        'days_before_holiday': 'mean'
    }).round(3)
    
    # Sort by count (frequency) and mean uplift
    holiday_performance = holiday_performance.sort_values(('ABI_MS_Uplift_Rel_Raw', 'count'), ascending=False)
    
    print(f"\nTop 70% Uplift Performance by Holiday:")
    print(f"{'Holiday':<20} {'Count':<6} {'Mean Uplift':<12} {'Std':<8} {'KSM Count':<10} {'Avg Days Before':<15}")
    print("-" * 85)
    
    holiday_results = {}
    for holiday in holiday_performance.index:
        count = holiday_performance.loc[holiday, ('ABI_MS_Uplift_Rel_Raw', 'count')]
        mean_uplift = holiday_performance.loc[holiday, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
        std_uplift = holiday_performance.loc[holiday, ('ABI_MS_Uplift_Rel_Raw', 'std')]
        ksm_count = holiday_performance.loc[holiday, ('KSM', 'sum')]
        avg_days_before = holiday_performance.loc[holiday, ('days_before_holiday', 'mean')]
        
        print(f"{holiday:<20} {count:<6} {mean_uplift:<12.3f} {std_uplift:<8.3f} {ksm_count:<10} {avg_days_before:<15.1f}")
        
        holiday_results[holiday] = {
            'count': count,
            'mean_uplift': mean_uplift,
            'std_uplift': std_uplift,
            'ksm_count': ksm_count,
            'avg_days_before': avg_days_before
        }
    
    return holiday_results, top_matches

def analyze_specific_holiday_examples(top_matches):
    """Analyze specific examples of high-performing holiday promotions"""
    print(f"\n" + "="*80)
    print("SPECIFIC HIGH-PERFORMING HOLIDAY PROMOTIONS")
    print("="*80)
    
    # Get top 20 highest uplifts with holiday matches
    top_examples = top_matches.nlargest(20, 'ABI_MS_Uplift_Rel_Raw')
    
    print(f"Top 20 highest uplifts with holiday correlation:")
    print(f"{'Date':<12} {'Uplift':<8} {'Holiday':<20} {'Days Before':<12} {'KSM':<4} {'Brand':<10}")
    print("-" * 80)
    
    for idx, row in top_examples.iterrows():
        date_str = row['ABI Start'].strftime('%Y-%m-%d')
        uplift = row['ABI_MS_Uplift_Rel_Raw']
        holiday = row['holiday_fr'][:19]  # Truncate long holiday names
        days_before = row['days_before_holiday']
        ksm = row['KSM']
        brand = row['Brand'][:9]  # Truncate long brand names
        
        print(f"{date_str:<12} {uplift:<8.3f} {holiday:<20} {days_before:<12} {ksm:<4} {brand:<10}")
    
    return top_examples

def analyze_seasonal_patterns(top_matches):
    """Analyze seasonal patterns in holiday-related uplifts"""
    print(f"\n" + "="*80)
    print("SEASONAL HOLIDAY PATTERNS")
    print("="*80)
    
    # Add month information
    top_matches['month'] = top_matches['holiday_date'].dt.month
    top_matches['month_name'] = top_matches['holiday_date'].dt.strftime('%B')
    
    # Analyze by month
    monthly_holiday_performance = top_matches.groupby('month_name').agg({
        'ABI_MS_Uplift_Rel_Raw': ['count', 'mean'],
        'KSM': 'sum'
    }).round(3)
    
    print(f"Holiday-related uplifts by month:")
    print(f"{'Month':<12} {'Count':<6} {'Mean Uplift':<12} {'KSM Count':<10}")
    print("-" * 45)
    
    for month in monthly_holiday_performance.index:
        count = monthly_holiday_performance.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'count')]
        mean_uplift = monthly_holiday_performance.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
        ksm_count = monthly_holiday_performance.loc[month, ('KSM', 'sum')]
        
        print(f"{month:<12} {count:<6} {mean_uplift:<12.3f} {ksm_count:<10}")

def generate_holiday_ksm_recommendations(holiday_results, top_matches):
    """Generate specific KSM recommendations based on holiday analysis"""
    print(f"\n" + "="*80)
    print("HOLIDAY-BASED KSM RECOMMENDATIONS")
    print("="*80)
    
    if not holiday_results:
        print("No holiday results to analyze!")
        return
    
    # Identify top-performing holidays
    top_holidays = sorted(holiday_results.items(), 
                         key=lambda x: (x[1]['count'], x[1]['mean_uplift']), 
                         reverse=True)[:10]
    
    print(f"🎯 TOP PERFORMING HOLIDAYS FOR KSM FOCUS:")
    print(f"{'Rank':<4} {'Holiday':<25} {'Count':<6} {'Mean Uplift':<12} {'Avg Days Before':<15}")
    print("-" * 70)
    
    recommendations = []
    for i, (holiday, stats) in enumerate(top_holidays, 1):
        count = stats['count']
        mean_uplift = stats['mean_uplift']
        avg_days_before = stats['avg_days_before']
        
        print(f"{i:<4} {holiday:<25} {count:<6} {mean_uplift:<12.3f} {avg_days_before:<15.1f}")
        
        if count >= 2:  # Only recommend holidays with multiple occurrences
            recommendations.append({
                'holiday': holiday,
                'count': count,
                'mean_uplift': mean_uplift,
                'optimal_start_days': int(avg_days_before)
            })
    
    print(f"\n🚀 SPECIFIC KSM IMPLEMENTATION RECOMMENDATIONS:")
    print("-" * 60)
    
    for rec in recommendations:
        print(f"✅ {rec['holiday']}:")
        print(f"   • Start promotions {rec['optimal_start_days']} days before the holiday")
        print(f"   • Expected uplift: {rec['mean_uplift']:.3f}")
        print(f"   • Historical success: {rec['count']} high-performing promotions")
        print()
    
    return recommendations

def main():
    """Main analysis function"""
    print("HOLIDAY KSM ANALYSIS")
    print("="*80)
    
    # Load data
    df_promo, df_holidays = load_data()
    
    # Identify top 70 percentile uplifts
    top_70_df, threshold_70 = identify_top_70_percentile(df_promo)
    
    # Match promotions to holidays (considering 7 days before)
    matches_df = match_promotions_to_holidays(df_promo, df_holidays, days_before=7)
    
    # Analyze holiday performance
    holiday_results, top_matches = analyze_holiday_performance(matches_df, top_70_df)
    
    if holiday_results:
        # Analyze specific examples
        top_examples = analyze_specific_holiday_examples(top_matches)
        
        # Analyze seasonal patterns
        analyze_seasonal_patterns(top_matches)
        
        # Generate recommendations
        recommendations = generate_holiday_ksm_recommendations(holiday_results, top_matches)
        
        print(f"\n" + "="*80)
        print("ANALYSIS COMPLETE!")
        print("="*80)
        print("Use these findings to refine your KSM strategy with specific holiday targeting.")
    
    return holiday_results, top_matches if holiday_results else (None, None)

if __name__ == "__main__":
    holiday_results, top_matches = main()
