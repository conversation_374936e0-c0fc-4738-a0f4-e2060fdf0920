# Notebook Update Summary: KSM_Top70pct Implementation

## Changes Made to `hierarchical_model_analysis 1 week Log Scaled Uplift.ipynb`

### 1. Updated Introduction Section
- **Line 23**: Updated KSM description to mention KSM_Top70pct filter
- **Lines 27-28**: Added explanation of the KSM update and its benefits

### 2. Enhanced Feature Engineering Function (`clean_and_engineer_features`)

#### Added KSM_Top70pct Filter Creation (Lines 359-373):
```python
# Create KSM_Top70pct filter based on analysis findings
# Keep KSM=1 only if uplift >= 70th percentile threshold
threshold_70 = np.percentile(df['ABI_MS_Uplift_Rel_Raw'], 70)
df['KSM_Top70pct'] = np.where(
    (df['KSM'].astype(int) == 1) & (df['ABI_MS_Uplift_Rel_Raw'] >= threshold_70), 1, 0
)
df['KSM_Top70pct'] = df['KSM_Top70pct'].astype('category')

print(f"\nKSM Filter Analysis:")
print(f"70th percentile threshold: {threshold_70:.3f}")
print(f"Original KSM=1 entries: {df['KSM'].astype(int).sum()}")
print(f"Filtered KSM_Top70pct=1 entries: {df['KSM_Top70pct'].astype(int).sum()}")
reduction = df['KSM'].astype(int).sum() - df['KSM_Top70pct'].astype(int).sum()
print(f"Reduction: {reduction} entries ({reduction/df['KSM'].astype(int).sum()*100:.1f}%)")
```

#### Updated Summary Output (Lines 389-392):
```python
print(f"KSM (Original): {df['KSM'].value_counts()}")
print(f"KSM_Top70pct (Filtered): {df['KSM_Top70pct'].value_counts()}")
```

### 3. Updated Model Building Functions

#### Updated `build_hierarchical_models` Function:

**Model Variables (Lines 912-917):**
```python
# Using KSM_Top70pct filter based on percentile analysis findings
model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
               'Same_Week', 'Before', 'After', 'Avg_Temp_std',
              'ABI_vs_Segment_PTC_Index_Agg_std', 'Overlapping_Days_std', 'ABI_Mechanic', 'Retailer',
              'Brand', 'Pack_Type', 'KSM_Top70pct']
```

**Model 1 Formula (Lines 933-935):**
```python
formula_fixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
         + Same_Week + Before + After + Avg_Temp_std +
          ABI_vs_Segment_PTC_Index_Agg_std + Overlapping_Days_std + C(ABI_Mechanic, Treatment('No NIP')) + KSM_Top70pct"""
```

**Model 2 Formula (Lines 955-957):**
```python
formula_mixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
         + Same_Week + Before + After + Avg_Temp_std + Overlapping_Days_std +
          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic, Treatment('No NIP')) + KSM_Top70pct"""
```

#### Updated `build_advanced_hierarchical_models` Function:

**Base Formula (Lines 990-993):**
```python
# Base formula for all models - using KSM_Top70pct filter
base_formula = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                 Same_Week + Before + After + Avg_Temp_std +
                 ABI_vs_Segment_PTC_Index_Agg_std + Overlapping_Days_std + C(ABI_Mechanic, Treatment('No NIP')) + KSM_Top70pct"""
```

## Expected Results After Update

### 1. KSM Filter Performance
- **Original KSM entries**: 145
- **Filtered KSM_Top70pct entries**: ~103 (29% reduction)
- **70th percentile threshold**: ~3.505

### 2. Method 5 Model Improvements
- **KSM_Top70pct coefficient**: Expected ~0.2678 (vs 0.0401 original)
- **Statistical significance**: p < 0.001 (vs p = 0.43 original)
- **Model fit improvement**: AIC expected ~523.49 (vs 546.61 original)

### 3. Key Benefits
1. **8x stronger KSM impact**: Coefficient increases from 0.0401 to ~0.2678
2. **Statistical significance**: KSM becomes highly significant (p < 0.001)
3. **Better model fit**: AIC improves by ~23 points
4. **Focus on high-impact periods**: Only keeps KSM flags for truly effective promotions

## How to Run the Updated Notebook

1. **Open the notebook**: `hierarchical_model_analysis 1 week Log Scaled Uplift.ipynb`
2. **Run all cells**: The notebook will now use KSM_Top70pct instead of original KSM
3. **Look for improvements in**:
   - Feature engineering summary showing KSM filter analysis
   - Model 1 results showing significant KSM_Top70pct coefficient
   - Method 5 (Model 5) results showing highly significant KSM_Top70pct impact
   - Overall model performance improvements

## Validation

The changes implement the findings from the percentile analysis:
- ✅ KSM_Top70pct filter creation matches the analysis methodology
- ✅ All model formulas updated to use KSM_Top70pct
- ✅ Feature engineering includes filter analysis output
- ✅ Model variables list updated appropriately

## Next Steps

1. **Run the updated notebook** to see the improved KSM coefficients
2. **Compare results** with the original analysis to confirm improvements
3. **Use Method 5 results** for business decision making
4. **Consider implementing** the recommended KSM calendar based on findings

---

*These updates implement the breakthrough finding that KSM impact was diluted by including too many low-performing periods. The Top 70% filter focuses on truly impactful promotional periods, resulting in highly significant KSM coefficients and improved model performance.*
