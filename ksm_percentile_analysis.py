#!/usr/bin/env python3
"""
KSM Percentile Analysis Script
Analyzes KSM impact by creating different percentile filters and running Method 5 hierarchical model
to identify when KSM coefficients peak and find patterns in high uplift periods.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# For hierarchical modeling
from statsmodels.regression.mixed_linear_model import MixedLM
from sklearn.preprocessing import StandardScaler
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_and_prepare_data():
    """Load and prepare the data with feature engineering"""
    print("Loading and preparing data...")
    df = pd.read_csv('demelted_data.csv')
    
    # Convert dates
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    
    # Extract date features
    df['Start_Month'] = df['ABI Start'].dt.month
    df['Start_Year'] = df['ABI Start'].dt.year
    df['Start_Month_Name'] = df['ABI Start'].dt.strftime('%B')
    df['Start_Day'] = df['ABI Start'].dt.day
    
    # Feature engineering (similar to notebook)
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days + 1
    
    # Extract pack type from ABI SKU
    df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\(12-15\)', case=False, na=False)
    df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\(20-24\)', case=False, na=False)
    df['Pack_Type'] = np.where(df['Pack_12_15'], '12-15',
                              np.where(df['Pack_20_24'], '20-24', 'Other'))
    
    # Extract brand from ABI SKU
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    df['Brand'] = df['Brand'].fillna('UNKNOWN')
    
    # Create timing variables
    df['Same_Week'] = df['Same Week'].astype(int)
    df['Before'] = df['1 wk before'].astype(int)
    df['After'] = df['1 wk after'].astype(int)
    
    # Clean numeric variables
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')
    df['Overlapping_Days'] = pd.to_numeric(df['overlapping days'], errors='coerce').fillna(0)
    
    # Target variable - handle infinities and outliers
    df['ABI_MS_Uplift_Rel_Raw'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')
    df = df.dropna(subset=['ABI_MS_Uplift_Rel_Raw'])
    
    # Handle extreme outliers (cap at 95th percentile * 3)
    uplift_95 = df['ABI_MS_Uplift_Rel_Raw'].quantile(0.95)
    df['ABI_MS_Uplift_Rel_Raw'] = np.where(df['ABI_MS_Uplift_Rel_Raw'] > uplift_95 * 3,
                                          uplift_95 * 3, df['ABI_MS_Uplift_Rel_Raw'])
    
    # Create log-scaled version
    min_uplift = df['ABI_MS_Uplift_Rel_Raw'].min()
    constant = 0.01 if min_uplift > 0 else abs(min_uplift) + 0.01
    df['ABI_MS_Uplift_Rel_Safe'] = df['ABI_MS_Uplift_Rel_Raw'] + constant
    df['ABI_MS_Uplift_Rel'] = np.log(df['ABI_MS_Uplift_Rel_Safe'])
    
    # Convert categorical variables
    df['Retailer'] = df['Retailer'].astype('category')
    df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')
    df['Brand'] = df['Brand'].astype('category')
    df['Pack_Type'] = df['Pack_Type'].astype('category')
    df['KSM'] = df['KSM'].astype(int)  # Ensure KSM is integer
    
    # Standardize continuous variables
    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg', 'Overlapping_Days']
    scaler = StandardScaler()
    
    for var in continuous_vars:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])
    
    print(f"Data prepared: {df.shape[0]} observations")
    print(f"Original KSM distribution: {df['KSM'].value_counts().sort_index()}")
    
    return df

def analyze_uplift_distribution(df):
    """Analyze the distribution of uplifts to understand percentiles"""
    print("\n" + "="*80)
    print("UPLIFT DISTRIBUTION ANALYSIS")
    print("="*80)
    
    uplift_col = 'ABI_MS_Uplift_Rel_Raw'
    
    # Basic statistics
    print(f"Uplift Statistics:")
    print(f"  Mean: {df[uplift_col].mean():.3f}")
    print(f"  Median: {df[uplift_col].median():.3f}")
    print(f"  Std Dev: {df[uplift_col].std():.3f}")
    print(f"  Min: {df[uplift_col].min():.3f}")
    print(f"  Max: {df[uplift_col].max():.3f}")
    
    # Calculate percentiles
    percentiles = [10, 20, 25, 30, 40, 50, 60, 70, 75, 80, 90, 95]
    percentile_values = {}
    
    print(f"\nPercentile Thresholds:")
    for p in percentiles:
        value = np.percentile(df[uplift_col], p)
        percentile_values[p] = value
        count_above = len(df[df[uplift_col] >= value])
        count_below = len(df[df[uplift_col] < value])
        print(f"  {p:2d}th percentile: {value:6.3f} ({count_above:3d} above, {count_below:3d} below)")
    
    return percentile_values

def create_ksm_percentile_filters(df, percentile_values):
    """Create different KSM filters based on uplift percentiles"""
    print("\n" + "="*80)
    print("CREATING KSM PERCENTILE FILTERS")
    print("="*80)
    
    uplift_col = 'ABI_MS_Uplift_Rel_Raw'
    percentiles_to_test = [10, 20, 25, 30, 40]
    
    ksm_filters = {}
    
    for percentile in percentiles_to_test:
        threshold = percentile_values[percentile]
        
        # Create new KSM filter: Keep KSM=1 only if uplift >= percentile threshold
        df[f'KSM_Top{100-percentile}pct'] = np.where(
            (df['KSM'] == 1) & (df[uplift_col] >= threshold), 1, 0
        )
        
        ksm_count = df[f'KSM_Top{100-percentile}pct'].sum()
        total_ksm_original = df['KSM'].sum()
        
        print(f"KSM_Top{100-percentile}pct filter (≥{threshold:.3f}):")
        print(f"  Original KSM=1 entries: {total_ksm_original}")
        print(f"  Filtered KSM=1 entries: {ksm_count}")
        print(f"  Reduction: {total_ksm_original - ksm_count} entries ({(total_ksm_original - ksm_count)/total_ksm_original*100:.1f}%)")
        
        ksm_filters[percentile] = f'KSM_Top{100-percentile}pct'
    
    return ksm_filters

def run_method5_with_ksm_filter(df, ksm_column):
    """Run Method 5 (Brand + Retailer Crossed Random Effects) with specific KSM filter"""
    
    # Prepare data for modeling
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                  'Same_Week', 'Before', 'After', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'Overlapping_Days_std', 
                  'ABI_Mechanic', 'Retailer', 'Brand', ksm_column]
    
    df_model = df[model_vars].dropna()
    
    # Base formula with the specific KSM column
    base_formula = f"""ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                      Same_Week + Before + After + Avg_Temp_std +
                      ABI_vs_Segment_PTC_Index_Agg_std + Overlapping_Days_std + 
                      C(ABI_Mechanic, Treatment('No NIP')) + {ksm_column}"""
    
    try:
        # Method 5: Crossed random effects (Retailer + Brand)
        vc = {"Brand": "0 + C(Brand)", "Retailer": "0 + C(Retailer)"}
        
        model = MixedLM.from_formula(base_formula, df_model,
                                   groups=df_model["Brand"],
                                   vc_formula=vc).fit()
        
        return model, df_model
    
    except Exception as e:
        print(f"Error fitting model with {ksm_column}: {e}")
        return None, df_model

def extract_ksm_coefficient(model, ksm_column):
    """Extract KSM coefficient and significance from model"""
    if model is None:
        return None, None, None
    
    try:
        # Get coefficient
        coef = model.params[ksm_column]
        
        # Get p-value
        pvalue = model.pvalues[ksm_column]
        
        # Get confidence interval
        conf_int = model.conf_int().loc[ksm_column]
        
        return coef, pvalue, conf_int
    
    except Exception as e:
        print(f"Error extracting coefficient for {ksm_column}: {e}")
        return None, None, None

def analyze_high_uplift_patterns(df, percentile_values):
    """Analyze patterns in high uplift entries for different percentiles"""
    print("\n" + "="*80)
    print("HIGH UPLIFT PATTERN ANALYSIS")
    print("="*80)
    
    uplift_col = 'ABI_MS_Uplift_Rel_Raw'
    percentiles_to_analyze = [20, 30, 40]
    
    patterns = {}
    
    for percentile in percentiles_to_analyze:
        threshold = percentile_values[percentile]
        high_uplift = df[df[uplift_col] >= threshold].copy()
        
        print(f"\n--- TOP {100-percentile}% UPLIFTS (≥{threshold:.3f}) ---")
        print(f"Total entries: {len(high_uplift)}")
        
        # Monthly pattern
        monthly_pattern = high_uplift.groupby('Start_Month_Name').agg({
            'ABI_MS_Uplift_Rel_Raw': ['count', 'mean'],
            'KSM': 'sum'
        }).round(3)
        
        print(f"\nMonthly Distribution:")
        for month in monthly_pattern.index:
            count = monthly_pattern.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'count')]
            mean_uplift = monthly_pattern.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
            ksm_count = monthly_pattern.loc[month, ('KSM', 'sum')]
            pct = count / len(high_uplift) * 100
            print(f"  {month:12}: {count:3d} entries ({pct:4.1f}%), avg uplift: {mean_uplift:.3f}, KSM: {ksm_count}")
        
        # Top specific dates
        top_dates = high_uplift.nlargest(10, uplift_col)[
            ['ABI Start', uplift_col, 'KSM', 'Start_Month_Name', 'Brand', 'Retailer']
        ]
        
        print(f"\nTop 10 Highest Uplifts in this percentile:")
        for idx, row in top_dates.iterrows():
            print(f"  {row['ABI Start'].strftime('%Y-%m-%d')}: {row[uplift_col]:.3f} (KSM: {row['KSM']}) - {row['Brand']} @ {row['Retailer'][:20]}")
        
        patterns[percentile] = {
            'monthly_pattern': monthly_pattern,
            'top_dates': top_dates,
            'total_entries': len(high_uplift)
        }
    
    return patterns

def main_analysis():
    """Main analysis function"""
    print("KSM PERCENTILE ANALYSIS")
    print("="*80)

    # Load and prepare data
    df = load_and_prepare_data()

    # Analyze uplift distribution
    percentile_values = analyze_uplift_distribution(df)

    # Create KSM percentile filters
    ksm_filters = create_ksm_percentile_filters(df, percentile_values)

    # Run Method 5 with different KSM filters
    print("\n" + "="*80)
    print("RUNNING METHOD 5 WITH DIFFERENT KSM FILTERS")
    print("="*80)

    results = {}

    # Test original KSM
    print(f"\n--- ORIGINAL KSM ---")
    model_orig, df_model = run_method5_with_ksm_filter(df, 'KSM')
    coef_orig, pval_orig, conf_orig = extract_ksm_coefficient(model_orig, 'KSM')

    if coef_orig is not None:
        print(f"KSM coefficient: {coef_orig:.4f}")
        print(f"P-value: {pval_orig:.4f}")
        print(f"Significant: {'Yes' if pval_orig < 0.05 else 'No'}")
        if conf_orig is not None:
            print(f"95% CI: [{conf_orig[0]:.4f}, {conf_orig[1]:.4f}]")

    results['original'] = {
        'coefficient': coef_orig,
        'pvalue': pval_orig,
        'confidence_interval': conf_orig,
        'model': model_orig
    }

    # Test each percentile filter
    for percentile, ksm_column in ksm_filters.items():
        print(f"\n--- {ksm_column.upper()} ---")

        model, df_model = run_method5_with_ksm_filter(df, ksm_column)
        coef, pval, conf = extract_ksm_coefficient(model, ksm_column)

        if coef is not None:
            print(f"KSM coefficient: {coef:.4f}")
            print(f"P-value: {pval:.4f}")
            print(f"Significant: {'Yes' if pval < 0.05 else 'No'}")
            if conf is not None:
                print(f"95% CI: [{conf[0]:.4f}, {conf[1]:.4f}]")

            # Calculate AIC for comparison
            try:
                aic = -2 * model.llf + 2 * len(model.params)
                print(f"AIC: {aic:.2f}")
            except:
                print("AIC: Could not calculate")

        results[percentile] = {
            'coefficient': coef,
            'pvalue': pval,
            'confidence_interval': conf,
            'model': model,
            'ksm_column': ksm_column
        }

    # Analyze high uplift patterns
    patterns = analyze_high_uplift_patterns(df, percentile_values)

    # Generate findings report
    generate_findings_report(results, patterns, df)

    return results, patterns, df

def generate_findings_report(results, patterns, df):
    """Generate a comprehensive findings report"""

    report_content = f"""# KSM Percentile Analysis Findings

## Executive Summary

This analysis tested different KSM (Key Selling Moments) filters based on uplift percentiles to identify when KSM coefficients peak in the Method 5 hierarchical model (Brand + Retailer Crossed Random Effects).

**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Total Observations:** {len(df)}
**Original KSM=1 entries:** {df['KSM'].sum()}

## Key Findings

### 1. KSM Coefficient Analysis by Percentile Filter

| Filter | KSM Entries | Coefficient | P-value | Significant | 95% CI |
|--------|-------------|-------------|---------|-------------|---------|"""

    # Add results to table
    for key, result in results.items():
        if result['coefficient'] is not None:
            filter_name = key if key == 'original' else f"Top {100-key}%"
            ksm_entries = df['KSM'].sum() if key == 'original' else df[result.get('ksm_column', 'KSM')].sum()
            coef = result['coefficient']
            pval = result['pvalue']
            significant = 'Yes' if pval < 0.05 else 'No'

            if result['confidence_interval'] is not None:
                ci_lower = result['confidence_interval'][0]
                ci_upper = result['confidence_interval'][1]
                ci_str = f"[{ci_lower:.4f}, {ci_upper:.4f}]"
            else:
                ci_str = "N/A"

            report_content += f"\n| {filter_name} | {ksm_entries} | {coef:.4f} | {pval:.4f} | {significant} | {ci_str} |"

    # Find peak coefficient
    valid_results = {k: v for k, v in results.items() if v['coefficient'] is not None}
    if valid_results:
        peak_key = max(valid_results.keys(), key=lambda k: abs(valid_results[k]['coefficient']))
        peak_result = valid_results[peak_key]

        report_content += f"""

### 2. Peak KSM Impact

**Highest coefficient magnitude:** {peak_result['coefficient']:.4f}
**Filter:** {'Original' if peak_key == 'original' else f'Top {100-peak_key}%'}
**Statistical significance:** {'Yes' if peak_result['pvalue'] < 0.05 else 'No'}

### 3. Monthly Patterns in High Uplifts

"""

        # Add monthly patterns
        for percentile, pattern_data in patterns.items():
            report_content += f"""
#### Top {100-percentile}% Uplifts ({pattern_data['total_entries']} entries)

**Monthly Distribution:**
"""
            monthly = pattern_data['monthly_pattern']
            for month in monthly.index:
                count = monthly.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'count')]
                mean_uplift = monthly.loc[month, ('ABI_MS_Uplift_Rel_Raw', 'mean')]
                ksm_count = monthly.loc[month, ('KSM', 'sum')]
                pct = count / pattern_data['total_entries'] * 100
                report_content += f"- **{month}**: {count} entries ({pct:.1f}%), avg uplift: {mean_uplift:.3f}, KSM count: {ksm_count}\n"

    # Add conclusions
    report_content += f"""

## Conclusions and Recommendations

### Key Insights:

1. **KSM Coefficient Behavior**: """

    # Analyze coefficient trend
    coef_values = [v['coefficient'] for v in valid_results.values() if v['coefficient'] is not None]
    if len(coef_values) > 1:
        if max(coef_values) > results['original']['coefficient']:
            report_content += "KSM coefficients increase when filtered to higher uplift percentiles, suggesting that KSM is more impactful during periods of naturally high uplift."
        else:
            report_content += "KSM coefficients do not improve significantly with percentile filtering, suggesting the current KSM definition may need refinement."

    report_content += f"""

2. **Statistical Significance**: """

    significant_filters = [k for k, v in valid_results.items() if v['pvalue'] < 0.05]
    if significant_filters:
        report_content += f"KSM becomes statistically significant with filters: {', '.join([str(k) if k != 'original' else 'Original' for k in significant_filters])}"
    else:
        report_content += "KSM remains statistically insignificant across all tested filters."

    report_content += f"""

3. **Recommended KSM Strategy**: Based on the coefficient analysis, """

    if peak_key != 'original' and peak_result['pvalue'] < 0.05:
        report_content += f"consider using the Top {100-peak_key}% filter as it shows the strongest and most significant KSM impact."
    else:
        report_content += "the current KSM definition may be too broad. Consider focusing on specific high-performing months or redefining KSM criteria."

    report_content += f"""

### Next Steps:

1. **Focus on High-Impact Months**: Based on the monthly patterns, prioritize promotions during months with consistently high uplift performance.
2. **Refine KSM Definition**: Consider creating a more selective KSM definition based on historical uplift performance rather than traditional seasonal assumptions.
3. **Brand-Retailer Specific Analysis**: The Method 5 model shows that brand-retailer combinations have significant random effects - consider KSM strategies tailored to specific brand-retailer pairs.

---

*Analysis completed using Method 5 (Brand + Retailer Crossed Random Effects) hierarchical model with {len(df)} promotion records.*
"""

    # Save report
    with open('KSM_Percentile_Analysis_Findings.md', 'w') as f:
        f.write(report_content)

    print(f"\n" + "="*80)
    print("FINDINGS REPORT GENERATED")
    print("="*80)
    print("Report saved to: KSM_Percentile_Analysis_Findings.md")

if __name__ == "__main__":
    results, patterns, df = main_analysis()
